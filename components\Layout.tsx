import { LinearGradient } from "expo-linear-gradient";
import React from "react";
import { RefreshControl, ScrollView, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import Gap from "./Gap";
import Header from "./Header";
import Loading from "./Loading";
import { RenderIf } from "./RenderIf";
import useAppAuth from "@/features/auth/hooks/useAppAuth";
import globalStyles from "@/lib/globalStyles";

type Props = {
  title?: {
    text: string;
    onPress?: () => void;
  };
  children: React.ReactNode;
  isLoading?: boolean;
  isRefreshing?: boolean;
  headless?: boolean;
  backButton?: boolean;
  showSearch?: boolean;
  onRefresh?: () => void;
  noScroll?: boolean;
  hasDarkerBg?: boolean;
  noMargin?: React.ReactNode;
  noPadding?: boolean;
  bottomChildren?: React.ReactNode;
  customGradient?: [string, string];
  headerFilter?: React.ReactNode;
};

const Layout = ({
  children,
  isLoading,
  isRefreshing,
  showSearch,
  title,
  onRefresh,
  headless = true,
  backButton = true,
  noScroll = false,
  hasDarkerBg = false,
  noMargin,
  noPadding = false,
  bottomChildren,
  customGradient,
  headerFilter,
}: Props) => {
  useAppAuth();
  const insets = useSafeAreaInsets();

  return (
    <View
      style={{
        flex: 1,
        flexDirection: "column",
        justifyContent: "flex-end",
        paddingTop: insets.top,
        overflowX: "hidden",
      }}
    >
      <LinearGradient
        colors={
          hasDarkerBg
            ? ["#E9E8ED", "#ceccd7"]
            : customGradient || ["white", "#E9E8ED"]
        }
        locations={[0.5, 0.9]}
        style={{
          width: "100%",
          height: "100%",
          position: "absolute",
          flex: 1,
          zIndex: -1,
          flexDirection: "column",
        }}
      />
      <RenderIf isTrue={!headless || !!title}>
        <Header
          title={title}
          backButton={backButton}
          style={{
            padding: globalStyles.gap.xs,
            paddingBottom: globalStyles.gap["2xs"],
            backgroundColor: "white",
            zIndex: 1,
          }}
          showSearch={showSearch}
        >
          {headerFilter}
        </Header>
      </RenderIf>
      {noScroll ? (
        <View
          style={{
            flex: 1,
            flexDirection: "column",
            paddingHorizontal: !noPadding ? globalStyles.gap.xs : 0,
          }}
        >
          {children}
          <Gap y={globalStyles.gap.xs} />
        </View>
      ) : (
        <ScrollView
          style={{
            flex: 1, // Allow ScrollView to fill available space
            paddingHorizontal: noMargin ? 0 : globalStyles.gap.xs,
          }}
          contentContainerStyle={{
            paddingBottom: insets.bottom, // Add padding for bottom safe area
          }}
          refreshControl={
            onRefresh ? (
              <RefreshControl
                refreshing={!!isRefreshing}
                onRefresh={onRefresh}
              />
            ) : undefined
          }
        >
          {noMargin ? (
            <>
              <View
                style={{
                  position: "relative",
                  zIndex: 10,
                  paddingHorizontal: globalStyles.gap.xs,
                }}
              >
                {children}
              </View>
              {noMargin}
            </>
          ) : (
            <>
              {children}
              <Gap y={globalStyles.gap.xs} />
            </>
          )}
        </ScrollView>
      )}
      {bottomChildren}
      {isLoading && <Loading />}
    </View>
  );
};

export default Layout;
