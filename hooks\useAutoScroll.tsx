import { useRef, useEffect, useCallback } from "react";
import { ScrollView } from "react-native";
import { MyEventType } from "@/features/events/model";
import { useFocusEffect } from "expo-router";

const useAutoScroll = ({
  SCROLL_WIDTH,
  events,
}: {
  events: MyEventType[];
  SCROLL_WIDTH: number;
}) => {
  const scrollRef = useRef<ScrollView>(null);
  const countRef = useRef(0);
  const intervalRef = useRef<ReturnType<typeof setInterval>>(null);
  const pages = Math.ceil(events.length / 3);

  const autoScroll = () => {
    intervalRef.current && clearInterval(intervalRef.current);

    intervalRef.current = setInterval(() => {
      if (!scrollRef.current) return;

      if (countRef.current >= pages) {
        countRef.current = 0;
      }

      scrollRef.current.scrollTo({
        x: SCROLL_WIDTH * countRef.current,
        animated: true,
      });

      countRef.current += 1;
    }, 3000);
  };

  const handleStopScroll = () => {
    intervalRef.current && clearInterval(intervalRef.current);
    countRef.current = 0;
  };

  useFocusEffect(
    useCallback(() => {
      if (!scrollRef.current || !pages || pages < 2) return;
      autoScroll();

      return () => {
        handleStopScroll();
      };
    }, [pages])
  );

  return { scrollRef, autoScroll, handleStopScroll };
};

export default useAutoScroll;
