import React from "react";
import { StyleProp, Text, TextStyle } from "react-native";
import globalStyles from "@/lib/globalStyles";

type Props = {
  children: JSX.Element;
  style?: StyleProp<TextStyle>;
};

const SectionHeading = ({ children, style }: Props) => {
  return (
    <Text
      style={[
        {
          fontSize: globalStyles.size["2xl"],
          fontWeight: "600",
          color: globalStyles.colors.dark.secondary,
        },
        style,
      ]}
    >
      {children}
    </Text>
  );
};

export default SectionHeading;
