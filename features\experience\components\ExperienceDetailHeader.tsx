import { View, Text } from "react-native";
import React from "react";
import { Octicons } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";
import { EXPERIENCE_TYPES, ExperienceType } from "@/features/experience/model";

const ExperienceDetailHeader = ({
  experience,
}: {
  experience: ExperienceType;
}) => {
  const { t } = useTranslation();

  const ExperienceIcon = EXPERIENCE_TYPES[experience.type]?.Icon;

  return (
    <View className="flex-col gap-2.5">
      <Text className="text-3xl font-bold text-dark-primary max-w-[80%]">
        {experience.name}
      </Text>
      <View className="flex-row flex-wrap items-center justify-between">
        {experience.provider && (
          <View className="flex-row items-center max-w-[80%] gap-2.5">
            <Octicons
              name="organization"
              className="text-light-secondary text-base"
            />
            <Text className="text-lg text-light-secondary">
              {experience.provider.name}
            </Text>
          </View>
        )}
        <View className="flex-row items-center gap-2">
          {ExperienceIcon && (
            <ExperienceIcon className="w-4 h-4 text-primary-1" />
          )}
          <Text className="text-base text-primary-1 font-medium">
            {EXPERIENCE_TYPES[experience.type]?.label(t)}
          </Text>
        </View>
      </View>
    </View>
  );
};

export default ExperienceDetailHeader;
