import { API_URLS, fetchApi } from "@/config/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { favoriteKey } from "./useHasUserFavoritedEvent";
import { createHandleErrorDialog } from "@/lib/errors";

type ToggleFavoriteParams = {
  eventId: string;
  userId: string;
};

const useToggleFavorite = ({ onSuccess }: { onSuccess?: () => void } = {}) => {
  const qClient = useQueryClient();

  const { mutate, isPending: isLoading } = useMutation({
    mutationFn: async (data: ToggleFavoriteParams) => {
      const res = await fetchApi<{ success: boolean }>(
        API_URLS.eventsFavorites,
        {
          method: "POST",
          body: JSON.stringify(data),
        }
      );
      return res.data;
    },
    onError: (e) =>
      createHandleErrorDialog({
        title: "Erro ao favoritar evento",
        message:
          "Ocorreu um erro ao colocar o evento como favorito, tente novamente daqui a pouco.",
        error: e as any,
      }),

    onSuccess: (res, data) => {
      qClient.invalidateQueries({
        queryKey: favoriteKey(data),
        refetchType: "all",
      });
      onSuccess?.();
    },
  });

  return {
    toggleFavorite: mutate,
    isLoading,
  };
};

export default useToggleFavorite;
