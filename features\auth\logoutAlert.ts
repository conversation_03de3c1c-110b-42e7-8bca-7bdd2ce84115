import { Alert } from "react-native";
import { TFunction } from "i18next";

/**
 * Creates a logout confirmation alert
 * @param t - Translation function
 * @param onConfirm - Function to call when user confirms logout
 */
export const createLogoutAlert = (
  t: TFunction<"translation", undefined>,
  onConfirm: () => void
) => {
  Alert.alert(
    t("auth.want_to_sign_out"),
    t("auth.want_to_sign_out_description"),
    [
      {
        text: t("common.yes"),
        onPress: onConfirm,
      },
      {
        text: t("common.no"),
      },
    ]
  );
};
