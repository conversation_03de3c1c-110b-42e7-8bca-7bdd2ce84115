import React from "react";
import { StyleSheet, View } from "react-native";

const AdsBanner = () => {
  return (
    <View style={styles.wrapper}>
      {/* <LinearGradient
        style={styles.gradient}
        colors={["transparent", "#c4c4c5"]}
        locations={[0.2, 0.8]}
      />
      <Pressable
        style={styles.container}
        onPress={() => WebBrowser.openBrowserAsync("https://zimbora.ao")}
      >
        <Image
          style={styles.image}
          source={require("@/assets/images/ads-2.jpg")}
        />
      </Pressable> */}
    </View>
  );
};

const styles = StyleSheet.create({
  wrapper: {
    position: "relative",
  },
  gradient: {
    position: "absolute",
    top: -80,
    zIndex: -1,
    width: "100%",
    height: 80,
  },
  container: {
    aspectRatio: 4 / 1.5,
    overflow: "hidden",
  },
  image: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
  },
});

export default AdsBanner;
