import React from "react";
import { View, StyleProp, ViewStyle, Text } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import globalStyles from "@/lib/globalStyles";
import { useTranslation } from "react-i18next";
import Button from "./Button";

type Props = {
  onPress?: () => void;
  isLoading?: boolean;
  style?: StyleProp<ViewStyle>;
  disabled?: boolean;
  variant?: "sign_in" | "sign_up";
};

const GoogleSignInButton = ({
  onPress,
  isLoading = false,
  style,
  disabled = false,
  variant = "sign_in",
}: Props) => {
  const { t } = useTranslation();

  const buttonText =
    variant === "sign_in"
      ? t("auth.sign_in_with_google")
      : t("auth.sign_up_with_google");

  return (
    <Button
      onPress={onPress}
      isLoading={isLoading}
      disabled={disabled}
      type="outline"
      style={[
        {
          backgroundColor: globalStyles.colors.white,
          borderColor: globalStyles.colors.light.secondary,
        },
        style,
      ]}
      textStyle={{
        color: globalStyles.colors.dark.secondary,
      }}
    >
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Ionicons
          name="logo-google"
          size={20}
          color={globalStyles.colors.dark.secondary}
          style={{ marginRight: globalStyles.gap["2xs"] }}
        />
        <Text
          style={{
            fontSize: globalStyles.size.lg,
            fontWeight: "500",
            color: globalStyles.colors.dark.secondary,
            marginTop: -2,
          }}
        >
          {buttonText}
        </Text>
      </View>
    </Button>
  );
};

export default GoogleSignInButton;
