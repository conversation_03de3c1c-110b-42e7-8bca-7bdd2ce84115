import React from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import globalStyles from "@/lib/globalStyles";
import { ExperienceType } from "@/features/experience/model";
import { useTranslation } from "react-i18next";
import * as Linking from "expo-linking";

const ExperienceDetailInfo = ({
  experience,
}: {
  experience: ExperienceType;
}) => {
  const { t } = useTranslation();

  const openMaps = () => {
    const mapLink = experience.locations?.[0]?.mapLink;
    if (mapLink) {
      Linking.openURL(mapLink);
    }
  };

  const getLocationText = () => {
    const location = experience.locations?.[0];
    if (!location) return "Location not available";
    return `${location.name}, ${location.province}`;
  };

  return (
    <View style={styles.container}>
      {/* Description */}
      <View style={styles.section}>
        <View style={styles.headerContainer}>
          <Ionicons
            name="document-text-outline"
            size={18}
            color={globalStyles.colors.dark.secondary}
          />
          <Text style={styles.sectionTitle}>{t("experience.description")}</Text>
        </View>
        <Text style={styles.description}>{experience.description}</Text>
      </View>

      {/* Price */}
      {experience.prices && (
        <View style={styles.section}>
          <View style={styles.headerContainer}>
            <Ionicons
              name="pricetag-outline"
              size={18}
              color={globalStyles.colors.dark.secondary}
            />
            <Text style={styles.sectionTitle}>{t("experience.prices")}</Text>
          </View>
          <Text style={styles.description}>
            {t("experience.starting_from")} 25 000 Kz
          </Text>
        </View>
      )}

      {/* Location */}
      <View style={styles.section}>
        <View style={styles.headerContainer}>
          <Ionicons
            name="location-outline"
            size={18}
            color={globalStyles.colors.dark.secondary}
          />
          <Text style={styles.sectionTitle}>{t("experience.location")}</Text>
        </View>
        <TouchableOpacity
          disabled={!experience.locations?.[0]?.mapLink}
          onPress={openMaps}
        >
          <Text
            style={[
              styles.locationText,
              experience.locations?.[0]?.mapLink &&
                styles.locationTextClickable,
            ]}
          >
            {getLocationText()}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Availability */}
      {experience.availability && experience.availability.length > 0 && (
        <View style={styles.section}>
          <View style={styles.headerContainer}>
            <Ionicons
              name="time-outline"
              size={18}
              color={globalStyles.colors.dark.secondary}
            />
            <Text style={styles.sectionTitle}>
              {t("experience.availability")}
            </Text>
          </View>
          {experience.availability.map((availability, index) => (
            <Text key={index} style={styles.description}>
              {availability.startAt} - {availability.endAt} (
              {availability.label})
            </Text>
          ))}
        </View>
      )}

      {/* Details */}
      {experience.details && experience.details.length > 0 && (
        <View style={styles.section}>
          <View style={styles.headerContainer}>
            <Ionicons
              name="information-circle-outline"
              size={18}
              color={globalStyles.colors.dark.secondary}
            />
            <Text style={styles.sectionTitle}>{t("experience.details")}</Text>
          </View>
          <View style={styles.detailsContainer}>
            {experience.details.map((detail, index) => (
              <Text key={index} style={styles.detailItem}>
                {detail.title}
              </Text>
            ))}
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "column",
    gap: globalStyles.gap.xs,
  },
  section: {
    flexDirection: "column",
    gap: globalStyles.gap["2xs"],
  },
  headerContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: globalStyles.gap["2xs"],
  },
  sectionTitle: {
    fontSize: globalStyles.size.lg,
    fontWeight: "600",
    color: globalStyles.colors.dark.primary,
  },
  description: {
    fontSize: globalStyles.size.lg,
    color: globalStyles.colors.tertiary2,
    lineHeight: globalStyles.size.lg + globalStyles.size.textAdjustLineHeight,
  },
  locationText: {
    fontSize: globalStyles.size.lg,
    color: globalStyles.colors.tertiary2,
  },
  locationTextClickable: {
    color: globalStyles.colors.primary1,
    fontWeight: "500",
  },
  detailsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: globalStyles.gap.xs,
  },
  detailItem: {
    fontSize: globalStyles.size.md,
    color: globalStyles.colors.tertiary2,
    textDecorationLine: "underline",
  },
});

export default ExperienceDetailInfo;
