import Button from "@/components/Button";
import GoogleSignInButton from "@/components/GoogleSignInButton";
import InputField from "@/components/InputField";
import StyledOtpInput from "@/components/StyledOtpInput";
import AuthHeader from "@/features/auth/components/AuthHeader";
import InfoLink from "@/features/auth/components/InfoLink";
import { createHandleErrorDialog } from "@/lib/errors";
import globalStyles from "@/lib/globalStyles";
import { useSignUp } from "@clerk/clerk-expo";
import { LinearGradient } from "expo-linear-gradient";
import { router } from "expo-router";
import { TFunction } from "i18next";
import React, { useCallback, useState, useTransition } from "react";
import { useTranslation } from "react-i18next";
import { ScrollView, View, Text } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { z } from "zod";
import useAppAuth from "@/features/auth/hooks/useAppAuth";
import * as WebBrowser from "expo-web-browser";

const signUpSchema = (t: TFunction<"translation", undefined>) =>
  z.object({
    email: z.string().email(t("auth.invalid_email")),
    firstName: z
      .string()
      .min(2, t("auth.first_name_min_length", { min: 2 }))
      .max(50, t("auth.first_name_max_length", { max: 50 })),
    lastName: z
      .string()
      .min(2, t("auth.last_name_min_length", { min: 2 }))
      .max(50, t("auth.last_name_max_length", { max: 50 })),
  });

const verificationSchema = (t: TFunction<"translation", undefined>) =>
  z.object({
    code: z.string().length(6, t("auth.code_length", { length: 6 })),
  });

WebBrowser.maybeCompleteAuthSession();

export default function SignUpScreen() {
  const { isLoaded, signUp, setActive } = useSignUp();
  const { handleGoogleOAuth, useWarmUpBrowser } = useAppAuth();
  const [pendingVerification, setPendingVerification] = useState(false);
  const [code, setCode] = useState("");

  const { t } = useTranslation();

  // Warm up browser for better UX
  useWarmUpBrowser();

  const [formFields, setFormFields] = useState({
    email: "",
    firstName: "",
    lastName: "",
  });

  const [isLoading, setIsLoading] = useState(false);
  const [isGooglePending, startGoogleTransition] = useTransition();

  const handleChange = (fieldName: keyof typeof formFields, value: string) => {
    setFormFields({
      ...formFields,
      [fieldName]: value,
    });
  };

  const handleSignUp = useCallback(async () => {
    try {
      if (!isLoaded) return;
      setIsLoading(true);

      const validatedData = signUpSchema(t).parse(formFields);

      await signUp.create({
        emailAddress: validatedData.email,
        firstName: validatedData.firstName,
        lastName: validatedData.lastName,
      });

      await signUp.prepareEmailAddressVerification({ strategy: "email_code" });
      setPendingVerification(true);
    } catch (err: any) {
      createHandleErrorDialog({
        title: t("auth.create_account_error"),
        error: err,
      });
    } finally {
      setIsLoading(false);
    }
  }, [isLoaded, formFields]);

  const handleVerifyCode = async (code: string) => {
    try {
      if (!isLoaded) return;
      setIsLoading(true);
      const validatedCode = verificationSchema(t).parse({ code });

      const signUpAttempt = await signUp.attemptEmailAddressVerification({
        code: validatedCode.code,
      });

      if (signUpAttempt.status === "complete") {
        await setActive({ session: signUpAttempt.createdSessionId });
        router.replace("/(tabs)");
      } else {
        createHandleErrorDialog({
          title: t("auth.verify_account_error"),
          error: new Error(t("auth.verify_account_error")),
        });
      }
    } catch (err: any) {
      createHandleErrorDialog({
        title: t("auth.verify_account_error"),
        error: err,
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignUp = () =>
    startGoogleTransition(async () => {
      await handleGoogleOAuth();
    });

  const showVerificationInput = () => {
    setPendingVerification(true);
  };

  return (
    <SafeAreaView
      style={{
        flex: 1,
        flexDirection: "column",
      }}
    >
      <LinearGradient
        colors={["#E9E8ED", "#ceccd7"]}
        locations={[0.5, 0.9]}
        style={{
          width: "100%",
          height: "100%",
          position: "absolute",
          flex: 1,
          zIndex: -1,
          flexDirection: "column",
        }}
      />
      <ScrollView
        style={{
          flexDirection: "column",
          flex: 1,
          paddingTop: globalStyles.size.pageTop,
          paddingHorizontal: globalStyles.gap.xs,
          gap: globalStyles.gap.xs,
        }}
        keyboardShouldPersistTaps="handled"
      >
        <AuthHeader description={t("auth.sign_up_description")} />
        <View
          style={{
            flexDirection: "column",
            gap: globalStyles.gap.xs,
            marginTop: globalStyles.gap.xs,
            marginBottom: 100,
          }}
        >
          {pendingVerification ? (
            <>
              <StyledOtpInput setCode={setCode} onSubmit={handleVerifyCode} />
              <Button
                isLoading={isLoading}
                text={t("common.verify")}
                style={{ width: "100%" }}
                onPress={() => handleVerifyCode(code)}
              />
              <Button
                text={t("common.back")}
                type="link"
                style={{ alignSelf: "center" }}
                onPress={() => setPendingVerification(false)}
              />
            </>
          ) : (
            <>
              <InputField
                required
                theme="secondary"
                placeholder={t("common.name")}
                value={formFields.firstName}
                onChangeText={(txt) => handleChange("firstName", txt)}
              />
              <InputField
                required
                theme="secondary"
                placeholder={t("common.last_name")}
                value={formFields.lastName}
                onChangeText={(txt) => handleChange("lastName", txt)}
              />
              <InputField
                required
                autoCapitalize="none"
                theme="secondary"
                placeholder={t("common.email")}
                autoComplete="email"
                value={formFields.email}
                onChangeText={(txt) => handleChange("email", txt.trim())}
              />
              <Button
                isLoading={isLoading}
                text={t("auth.sign_up")}
                style={{ width: "100%" }}
                onPress={handleSignUp}
              />

              {/* Divider */}
              <View
                style={{
                  flexDirection: "row",
                  alignItems: "center",
                  marginVertical: 5,
                }}
              >
                <View
                  style={{
                    flex: 1,
                    height: 1,
                    backgroundColor: globalStyles.rgba({ opacity: 0.4 }).light
                      .secondary,
                  }}
                />
                <Text
                  style={{
                    marginHorizontal: globalStyles.gap["2xs"],
                    fontSize: globalStyles.size.md,
                    color: globalStyles.rgba().light.secondary,
                  }}
                >
                  {t("auth.or_continue_with")}
                </Text>
                <View
                  style={{
                    flex: 1,
                    height: 1,
                    backgroundColor: globalStyles.rgba({ opacity: 0.4 }).light
                      .secondary,
                  }}
                />
              </View>

              {/* Google Sign Up Button */}
              <GoogleSignInButton
                variant="sign_up"
                onPress={handleGoogleSignUp}
                isLoading={isGooglePending}
                style={{ width: "100%" }}
              />

              {/* <Button
                text={t("common.back")}
                type="link"
                style={{ alignSelf: "center" }}
                onPress={() => router.back()}
              /> */}
              <InfoLink
                description={t("auth.already_have_the")}
                linkText={t("auth.confirmation_code")}
                onPress={showVerificationInput}
              />
              <InfoLink
                description={t("auth.already_has_an_account")}
                linkText={t("common.enter")}
                onPress={() => router.push("/sign-in")}
              />
            </>
          )}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
