import { ActivityIndicator, Text, View, StyleSheet } from "react-native";
import globalStyles from "@/lib/globalStyles";
import { useTranslation } from "react-i18next";

const Loading = () => {
  const { t } = useTranslation();

  return (
    <View style={styles.container}>
      <ActivityIndicator size="large" color={globalStyles.colors.primary2} />
      <Text style={styles.text}>{t("common.loading")}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    flex: 1,
    flexDirection: "column",
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "white",
  },
  text: {
    fontSize: globalStyles.size["2xl"],
    marginTop: globalStyles.gap.xs,
    color: globalStyles.colors.primary1,
  },
});

export default Loading;
