import { ExpoConfig, ConfigContext } from "expo/config";

export default ({ config }: ConfigContext): ExpoConfig => ({
  ...config,
  name: config.name || "zimbora", // Ensure name is always defined
  slug: config.slug || "zimbora", // Ensure slug is always defined
  android: {
    ...config.android,
    googleServicesFile:
      process.env.GOOGLE_SERVICES_JSON || "./google-services.json",
  },
  extra: {
    ...config.extra,
    INTERNAL_API_KEY: process.env.INTERNAL_API_KEY,
  },
});
