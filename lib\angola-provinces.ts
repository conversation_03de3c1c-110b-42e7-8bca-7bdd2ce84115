export const DEFAULT_PROVINCE = "luanda";

export const ANGOLA_PROVINCES = {
  [DEFAULT_PROVINCE]: "Luanda",
  bengo: "Bengo",
  benguela: "Ben<PERSON><PERSON>",
  bie: "<PERSON><PERSON><PERSON>",
  cabinda: "<PERSON>abi<PERSON>",
  "cuando-cubango": "Cuando Cubango",
  "cuanza-norte": "Cuanza Norte",
  "cuanza-sul": "Cuanza Sul",
  cunene: "Cunene",
  huambo: "Huambo",
  huila: "Huíla",
  "lunda-norte": "Lunda Norte",
  "lunda-sul": "Lunda Sul",
  malanje: "Malanje",
  moxico: "Moxico",
  namibe: "Namibe",
  uige: "Uíge",
  zaire: "Zaire",
} as const;

export type AngolaProvinceKey = keyof typeof ANGOLA_PROVINCES;
export type AngolaProvinceLabel =
  (typeof ANGOLA_PROVINCES)[keyof typeof ANGOLA_PROVINCES];

export const getProvinceLabel = (key: AngolaProvinceKey): AngolaProvinceLabel =>
  ANGOLA_PROVINCES[key] || ANGOLA_PROVINCES.luanda;

export const getProvinceKey = (label: string): AngolaProvinceKey => {
  const provinceKey = Object.entries(ANGOLA_PROVINCES).find(
    ([_, value]) => value === label
  )?.[0] as AngolaProvinceKey | undefined;

  return provinceKey || DEFAULT_PROVINCE;
};
