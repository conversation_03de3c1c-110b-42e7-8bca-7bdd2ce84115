import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import globalStyles from "@/lib/globalStyles";
import { useTranslation } from "react-i18next";

const ExperienceDetailFeatures = ({ features }: { features: string[] }) => {
  const { t } = useTranslation();

  if (!features || features.length === 0) {
    return null;
  }

  return (
    <View style={styles.container}>
      <View style={styles.headerContainer}>
        <Ionicons
          name="star-outline"
          size={18}
          color={globalStyles.colors.dark.secondary}
        />
        <Text style={styles.title}>{t("experience.highlights")}</Text>
      </View>
      <View style={styles.featuresContainer}>
        {features.map((feature, index) => (
          <View key={index} style={styles.featureItem}>
            <Ionicons
              name="checkmark"
              size={16}
              color="#4ade80"
            />
            <Text style={styles.featureText}>{feature}</Text>
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "column",
    gap: globalStyles.gap["2xs"],
  },
  headerContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: globalStyles.gap["2xs"],
  },
  title: {
    fontSize: globalStyles.size.lg,
    fontWeight: "600",
    color: globalStyles.colors.dark.primary,
  },
  featuresContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: globalStyles.gap["2xs"],
  },
  featureItem: {
    flexDirection: "row",
    alignItems: "center",
    width: "48%",
    marginBottom: globalStyles.gap["2xs"] / 2,
    gap: globalStyles.gap["2xs"] / 2,
  },
  featureText: {
    fontSize: globalStyles.size.md,
    color: globalStyles.colors.tertiary2,
    flex: 1,
  },
});

export default ExperienceDetailFeatures;
