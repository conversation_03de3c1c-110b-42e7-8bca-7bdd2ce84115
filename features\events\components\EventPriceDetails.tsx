import { MyEventType } from "@/features/events/model";
import globalStyles from "@/lib/globalStyles";
import { MaterialCommunityIcons } from "@expo/vector-icons";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  Modal,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { RenderIf } from "../../../components/RenderIf";

type Props = {
  event: MyEventType;
};

const inKwanza = (value: number) =>
  new Intl.NumberFormat("pt-AO", {
    style: "decimal",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);

const EventPriceDetails = ({ event }: Props) => {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [currPrice, setCurrPrice] = useState(event?.prices?.[0] || null);

  return (
    <>
      <View style={styles.priceContainer}>
        <Text style={styles.currencyText}>Kz</Text>
        <RenderIf isTrue={event?.prices?.length >= 1}>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={{
              flexDirection: "row",
              alignItems: "center",
              gap: globalStyles.gap["2xs"] + 5,
            }}
            fadingEdgeLength={100}
          >
            {event?.prices.map((_p, i) => (
              <TouchableOpacity
                key={_p.name + i}
                style={styles.priceButton}
                onPress={() => {
                  setCurrPrice(_p);
                  setIsOpen(true);
                }}
              >
                <Text style={styles.priceText}>
                  {_p.price === 0
                    ? t("event.free_entry")
                    : inKwanza(_p.price) + " Kz"}
                </Text>

                <MaterialCommunityIcons
                  name="information-outline"
                  size={16}
                  color={globalStyles.colors.primary1}
                />
              </TouchableOpacity>
            ))}
          </ScrollView>
        </RenderIf>
        <RenderIf isTrue={!event?.prices?.length}>
          <Text style={styles.freeText}>{t("common.free")}</Text>
        </RenderIf>
      </View>
      <ModalPriceInfo
        price={currPrice}
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
      />
    </>
  );
};

const ModalPriceInfo = ({
  isOpen,
  price,
  onClose,
}: {
  isOpen: boolean;
  onClose: () => void;
  price: MyEventType["prices"][0];
}) => {
  const { t } = useTranslation();

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={isOpen}
      onRequestClose={onClose}
      hardwareAccelerated={true}
    >
      <View style={styles.modalContainer}>
        <TouchableOpacity style={styles.modalOverlay} onPress={onClose} />
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>{t("event.price_info")}</Text>
          <Text>{price?.description || t("common.no_description")}</Text>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  priceContainer: {
    flexDirection: "row",
    justifyContent: "flex-start",
    alignItems: "center",
    gap: globalStyles.gap["2xs"] + 5,
  },
  currencyText: {
    color: globalStyles.colors.tertiary2,
    fontSize: globalStyles.size.xl,
    fontWeight: "bold",
  },
  priceButton: {
    flexDirection: "row",
    marginTop: 3,
    justifyContent: "flex-start",
    alignItems: "center",
    borderColor: globalStyles.colors.primary1,
    borderWidth: 1,
    borderRadius: globalStyles.rounded.full,
    paddingHorizontal: globalStyles.gap["2xs"],
    paddingVertical: 3,
  },
  priceText: {
    fontSize: globalStyles.size.lg,
    marginRight: globalStyles.gap["2xs"],
    marginTop: -1,
    fontWeight: "600",
    color: globalStyles.colors.primary1,
  },
  freeText: {
    fontSize: globalStyles.size.xl,
    marginLeft: globalStyles.gap["2xs"],
    fontWeight: "bold",
    color: globalStyles.colors.tertiary2,
  },
  modalContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  modalOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0, 0, 0, 0.3)",
  },
  modalContent: {
    flexDirection: "column",
    justifyContent: "flex-start",
    alignItems: "flex-start",
    backgroundColor: "white",
    borderRadius: globalStyles.rounded.sm,
    padding: globalStyles.gap.xs,
    minWidth: "50%",
    width: "85%",
    elevation: 5,
    gap: 5,
  },
  modalTitle: {
    fontWeight: "bold",
    fontSize: globalStyles.size.xl,
    marginBottom: globalStyles.gap["2xs"],
  },
});

export default EventPriceDetails;
