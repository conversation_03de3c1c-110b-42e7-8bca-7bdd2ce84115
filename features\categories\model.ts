import { z } from "zod";

export const CATEGORY_TYPES = {
  NORMAL: "Normal",
  CUSTOM: "Personalizado",
} as const;

export const myCategorySchema = z.object({
  _id: z.string(),
  _type: z.literal("category"),
  _createdAt: z.string().datetime(),
  _updatedAt: z.string().datetime(),
  name: z.string().max(30, "Nome deve ter no máximo 30 caracteres"),
  slug: z.object({
    _type: z.literal("slug"),
    current: z.string(),
  }),
  description: z
    .string()
    .max(500, "Descrição deve ter no máximo 500 caracteres"),
  type: z
    .enum(Object.keys(CATEGORY_TYPES) as [keyof typeof CATEGORY_TYPES])
    .default("NORMAL"),
  events: z
    .array(
      z.object({
        _type: z.literal("reference"),
        _ref: z.string(),
      })
    )
    .optional(),
  createdBy: z
    .string()
    .max(50, "Nome do criador deve ter no máximo 50 caracteres"),
});

export type MyCategoryType = z.infer<typeof myCategorySchema>;

export type MyEventCategory = Pick<MyCategoryType, "name" | "_id">;

export type CategoryFilterFields = {
  // searchKey?: string;
  categoryId?: string;
  sortBy?: keyof Omit<MyCategoryType, "createdBy" | "createdById">;
  type?: MyCategoryType["type"];
};
