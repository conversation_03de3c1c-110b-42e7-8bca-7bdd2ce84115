import { MyEventType } from "@/features/events/model";
import { screenWidth } from "@/lib/device";
import React from "react";
import { ImageBackground, StyleSheet, Text, View } from "react-native";
import { buttonStyles } from "../../../components/Button";
import globalStyles from "@/lib/globalStyles";
import { Link } from "expo-router";
import { useTranslation } from "react-i18next";

type Props = {
  event: MyEventType;
};

export const ITEM_WIDTH = screenWidth - 40;

const EventSponsoredCard = ({ event }: Props) => {
  const { t } = useTranslation();
  const media = event.medias?.[0];

  const imageUrl = media?.urlBanner ? media.urlBanner : media?.url;

  return (
    <ImageBackground
      resizeMode="cover"
      style={styles.container}
      source={
        imageUrl && imageUrl !== null
          ? { uri: imageUrl }
          : require("@/assets/images/event-placeholder.jpg")
      }
    >
      <View style={styles.backdrop} />
      <View
        style={{
          flexDirection: "column",
          gap: 5,
        }}
      >
        <Text style={styles.title}>{event.name}</Text>
        <Text style={styles.subText} numberOfLines={2}>
          {event.description}
        </Text>
      </View>
      <Link
        href={{ pathname: "/events/[id]", params: { id: event._id } }}
        style={[
          buttonStyles.button,
          buttonStyles.primaryButton,
          {
            padding: globalStyles.size.xs,
          },
        ]}
      >
        <Text
          style={{
            textAlign: "center",
            color: "white",
          }}
        >
          {t("event.learn_more")}
        </Text>
      </Link>
    </ImageBackground>
  );
};

const styles = StyleSheet.create({
  container: {
    position: "relative",
    flexShrink: 0,
    justifyContent: "flex-end",
    gap: globalStyles.size["3xl"],
    padding: globalStyles.gap.xs,
    borderRadius: globalStyles.rounded.sm,
    backgroundColor: globalStyles.colors.light.primary,
    width: ITEM_WIDTH,
    aspectRatio: 4 / 2,
    overflow: "hidden",
  },
  backdrop: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: globalStyles.colors.dark.primary,
    opacity: 0.5,
  },
  title: {
    fontSize: globalStyles.size["2xl"],
    color: globalStyles.colors.white,
    fontWeight: "bold",
  },
  subText: {
    fontSize: globalStyles.size["md"],
    color: globalStyles.colors.light.primary,
    fontWeight: "500",
  },
  link: {
    backgroundColor: globalStyles.colors.light.primary,
    padding: globalStyles.gap.xs,
    borderRadius: globalStyles.rounded.sm,
  },
  linkText: {
    color: globalStyles.colors.white,
    fontWeight: "bold",
  },
});

export default EventSponsoredCard;
