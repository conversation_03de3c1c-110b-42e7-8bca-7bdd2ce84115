import { screenWidth } from "@/lib/device";
import globalStyles from "@/lib/globalStyles";
import { forwardRef } from "react";
import {
  StyleProp,
  StyleSheet,
  TextInput,
  TextInputProps,
  View,
  ViewStyle,
} from "react-native";

type Props = TextInputProps & {
  required?: boolean;
  theme?: "primary" | "secondary";
  containerStyle?: StyleProp<ViewStyle>;
};

const InputField = forwardRef<TextInput, Props>(
  ({ style, theme, containerStyle, ...props }, ref) => {
    const isSecondary = theme === "secondary";
    return (
      <View style={[styles.container, containerStyle]}>
        {props.required && <View style={styles.required} />}
        <TextInput
          ref={ref}
          {...props}
          style={[
            styles.input,
            {
              backgroundColor: isSecondary
                ? globalStyles.colors.white
                : globalStyles.colors.light.primary,
            },
            style,
          ]}
        />
      </View>
    );
  }
);

const styles = StyleSheet.create({
  container: {
    position: "relative",
    width: "100%",
    flexShrink: 1,
  },
  required: {
    position: "absolute",
    top: 0,
    left: 0,
    width: screenWidth * 0.015,
    height: screenWidth * 0.015,
    borderRadius: globalStyles.rounded.xs,
    backgroundColor: globalStyles.colors.primary2,
    zIndex: 1,
  },
  input: {
    backgroundColor: globalStyles.colors.light.primary,
    borderRadius: globalStyles.rounded.xs,
    fontSize: globalStyles.size.lg,
    paddingHorizontal: globalStyles.gap.xs,
    paddingVertical: globalStyles.gap["2xs"],
    width: "100%",
    flexShrink: 1,
    color: globalStyles.colors.primary1,
  },
});

export default InputField;
