import { useDebounce } from "@/hooks/useDebounce";
import globalStyles from "@/lib/globalStyles";
import { Ionicons } from "@expo/vector-icons";
import React, { useEffect, useRef } from "react";
import { useTranslation } from "react-i18next";
import { Pressable, TextInput, View } from "react-native";
import InputField from "./InputField";

type HeaderExperiencesFilterProps = {
  onSearch?: (text: string) => void;
  onFilterPress?: () => void;
  searchValue?: string;
  searchPlaceholder?: string;
};

const HeaderExperiencesFilter = ({
  onSearch,
  onFilterPress,
  searchValue = "",
  searchPlaceholder,
}: HeaderExperiencesFilterProps) => {
  const inputSearchRef = useRef<TextInput>(null);
  const { t } = useTranslation();

  const debouncedHandleSearch = useDebounce((text: string) => {
    onSearch?.(text);
  }, 1000);

  useEffect(() => {
    if (!searchValue && inputSearchRef.current) {
      inputSearchRef.current.clear();
    }
  }, [searchValue]);

  return (
    <View
      style={{
        flexDirection: "row",
        alignItems: "center",
        gap: globalStyles.gap["2xs"],
      }}
    >
      <InputField
        placeholder={searchPlaceholder || t("common.search")}
        onChangeText={debouncedHandleSearch}
        defaultValue={searchValue}
        ref={inputSearchRef}
        style={{ borderRadius: globalStyles.rounded.full }}
      />

      <Pressable onPress={onFilterPress}>
        <Ionicons
          name="filter"
          size={24}
          color={globalStyles.colors.primary1}
        />
      </Pressable>
    </View>
  );
};

export default HeaderExperiencesFilter;
