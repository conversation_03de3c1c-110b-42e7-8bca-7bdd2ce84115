import useGetQuery from "@/hooks/useGetQuery";
import {
  CategoryFilterFields,
  MyEventCategory,
} from "@/features/categories/model";
import { API_URLS } from "@/config/api";

export const QUERY_CATEGORIES_KEY = "eventsCategories";

const useGetEventsCategories = (query?: CategoryFilterFields) => {
  const { data, ...rest } = useGetQuery<MyEventCategory[]>({
    apiUrl: API_URLS.eventsCategories,
    queryKey: QUERY_CATEGORIES_KEY,
    initialData: [],
    query,
  });

  return {
    categories: data,
    ...rest,
  };
};

export default useGetEventsCategories;
