import React, { useState } from "react";
import { <PERSON><PERSON>, Share, StyleSheet, View } from "react-native";
import Button from "@/components/Button";
import Gap from "@/components/Gap";
import Layout from "@/components/Layout";
import ExperienceDetailHeader from "@/features/experience/components/ExperienceDetailHeader";
import ExperienceDetailImage from "@/features/experience/components/ExperienceDetailImage";
import ExperienceDetailFeatures from "@/features/experience/components/ExperienceDetailFeatures";
import ExperienceDetailInfo from "@/features/experience/components/ExperienceDetailInfo";
import globalStyles from "@/lib/globalStyles";
import Loading from "@/components/Loading";
import { useLocalSearchParams, router } from "expo-router";
import { SITE_URL } from "@/config/api";
import { useTranslation } from "react-i18next";
import { ExperienceType } from "@/features/experience/model";

const ExperienceDetailsScreen = () => {
  const { t } = useTranslation();
  const { id } = useLocalSearchParams();
  const [isItemsModalOpen, setIsItemsModalOpen] = useState(false);

  if (!id || typeof id !== "string") {
    router.replace("/");
    throw new Error("Invalid experience id");
  }

  // Mock data - replace with actual data fetching based on id
  const experience: ExperienceType = {
    _id: id,
    _type: "experience",
    _createdAt: "2024-01-01T00:00:00Z",
    _updatedAt: "2024-01-01T00:00:00Z",
    name: "Resort Namibe",
    description:
      "O Resort Namibe, localizado em uma das regiões mais bonitas de Angola, oferece vistas deslumbrantes sobre o vasto Oceano Atlântico. Com acesso direto à praia de areia branca e fina, os hóspedes podem desfrutar de momentos relaxantes à beira-mar. As acomodações de luxo são projetadas para proporcionar conforto e elegância, garantindo uma experiência inesquecível em meio à natureza exuberante.",
    type: "ACTIVITY",
    locations: [
      {
        name: "Ladeira, Bairro Novo",
        province: "namibe",
        mapLink: "https://maps.google.com",
      },
    ],
    medias: [
      {
        url: "https://example.com/resort-image-1.jpg",
        urlSmall: "https://example.com/resort-image-1-small.jpg",
        urlBanner: "https://example.com/resort-image-1-banner.jpg",
      },
      {
        url: "https://example.com/resort-image-2.jpg",
        urlSmall: "https://example.com/resort-image-2-small.jpg",
        urlBanner: "https://example.com/resort-image-2-banner.jpg",
      },
    ],
    provider: {
      _id: "provider-1",
      name: "Propriedade de Família Almeida",
      contacts: [
        {
          type: "WHATSAPP",
          value: "+244 923 456 789",
        },
        {
          type: "EMAIL",
          value: "<EMAIL>",
        },
        {
          type: "PHONE",
          value: "+244 923 456 789",
        },
      ],
    },
    features: [
      { name: "Piscina exterior" },
      { name: "Bar" },
      { name: "Estacionamento gratuito" },
      { name: "Acesso Wi-Fi gratuito" },
      { name: "Pequeno-almoço" },
    ],
    details: [
      {
        title: "Geral",
        description: "Informações gerais sobre o resort",
      },
      {
        title: "Área ao ar livre",
        description: "Espaços externos e atividades",
      },
      {
        title: "Acessibilidade",
        description: "Facilidades para pessoas com mobilidade reduzida",
      },
    ],
    prices: [
      {
        type: "ABSOLUTE",
        price: 25000,
        description: "Preço por noite (quarto duplo)",
      },
    ],
    checkoutMethods: [
      {
        type: "WHATSAPP",
        value: "+244 923 456 789",
        name: "WhatsApp",
      },
      {
        type: "EMAIL",
        value: "<EMAIL>",
        name: "Email",
      },
    ],
    highlightedUntil: "2024-12-31T23:59:59Z",
    sponsoredUntil: "2024-12-31T23:59:59Z",
    availability: [
      {
        startAt: "6:00 AM",
        endAt: "10:30 PM",
        label: "Segunda à Sexta",
        status: "AVAILABLE",
      },
      {
        startAt: "10:00 AM",
        endAt: "6:30 PM",
        label: "Finais de semana e feriados",
        status: "AVAILABLE",
      },
    ],
    items: [
      {
        name: "Quarto Standard",
        description:
          "Quarto confortável com vista para o jardim, equipado com todas as comodidades necessárias para uma estadia relaxante.",
      },
      {
        name: "Quarto Deluxe",
        description:
          "Quarto luxuoso com vista para o mar, oferecendo uma experiência premium com acabamentos de alta qualidade.",
      },
      {
        name: "Suíte Presidencial",
        description:
          "Suíte premium com terraço privativo e vista panorâmica, ideal para ocasiões especiais e máximo conforto.",
      },
    ],
  };

  const isLoading = false;

  if (!experience && !isLoading) {
    router.replace("/");
    return null;
  }

  if (isLoading || !experience) {
    return <Loading />;
  }

  const onShare = async () => {
    try {
      const result = await Share.share({
        message: `${SITE_URL}/experiences/${experience._id}`,
      });

      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          // shared with activity type of result.activityType
        } else {
          // shared
        }
      } else if (result.action === Share.dismissedAction) {
        // dismissed
      }
    } catch (error: any) {
      Alert.alert(error.message);
    }
  };

  const onViewItems = () => {
    setIsItemsModalOpen(true);
  };

  const onContact = () => {
    // TODO: Implement contact functionality
    Alert.alert("Contact", "Contact functionality to be implemented");
  };

  const featuresArray = experience.features?.map((f) => f.name) || [];
  const hasItems = !!(experience.items && experience.items.length > 0);

  return (
    <>
      <Layout headless={false}>
        <View className="gap-5 flex-col">
          <ExperienceDetailHeader experience={experience} />

          <ExperienceDetailImage
            experience={experience}
            onShare={onShare}
            onViewItems={onViewItems}
            hasItems={hasItems}
          />

          <ExperienceDetailFeatures features={featuresArray} />

          <ExperienceDetailInfo experience={experience} />
          <Gap y={globalStyles.gap.lg} />
        </View>
      </Layout>

      <Button
        text={t("experience.contact")}
        style={styles.contactBtn}
        onPress={onContact}
      />
    </>
  );
};

const styles = StyleSheet.create({
  contactBtn: {
    position: "absolute",
    width: `${100 - globalStyles.gap["2xs"]}%`,
    bottom: 30,
    alignSelf: "center",
  },
});

export default ExperienceDetailsScreen;
