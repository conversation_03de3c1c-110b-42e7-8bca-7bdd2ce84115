import {
  View,
  Text,
  Modal,
  StyleSheet,
  TouchableOpacity,
  Linking,
} from "react-native";
import React from "react";
import { EventCheckoutMethodsType, MyEventType } from "@/features/events/model";
import { RenderIf } from "../../../components/RenderIf";
import { FontAwesome } from "@expo/vector-icons";
import globalStyles from "@/lib/globalStyles";
import Gap from "../../../components/Gap";
import Button from "../../../components/Button";
import { useTranslation } from "react-i18next";

type Props = {
  event: MyEventType;
  checkouts: EventCheckoutMethodsType[];
  isOpen: boolean;
  onClose: () => void;
};

const CheckoutItem = ({
  type,
  title,
  onPress,
}: {
  title: string;
  type: EventCheckoutMethodsType["type"];
  onPress: () => void;
}) => {
  return (
    <TouchableOpacity
      style={{
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "flex-start",
        gap: globalStyles.size.xs,
      }}
      onPress={onPress}
    >
      <RenderIf isTrue={type === "WHATSAPP"}>
        <FontAwesome name="whatsapp" size={24} color="green" />
      </RenderIf>
      <RenderIf isTrue={type === "EMAIL"}>
        <FontAwesome
          name="envelope"
          size={24}
          color={globalStyles.rgba().light.secondary}
        />
      </RenderIf>
      <RenderIf isTrue={type === "WEBSITE"}>
        <FontAwesome
          name="globe"
          size={24}
          color={globalStyles.rgba().light.secondary}
        />
      </RenderIf>
      <RenderIf isTrue={type === "PHONE"}>
        <FontAwesome
          name="phone"
          size={24}
          color={globalStyles.rgba().light.secondary}
        />
      </RenderIf>
      <Text
        style={{
          color: globalStyles.rgba().dark.primary,
          fontSize: globalStyles.size.xl,
          lineHeight: globalStyles.size.textAdjustLineHeight + 2,
        }}
      >
        {title}
      </Text>
    </TouchableOpacity>
  );
};

const CheckoutModal = ({ checkouts, event, isOpen, onClose }: Props) => {
  const { t } = useTranslation();

  const handleOnPress = (eventCheckout: EventCheckoutMethodsType) => {
    const message = t("event.participate_message", {
      eventName: event.name,
    });
    if (eventCheckout.type === "WHATSAPP") {
      Linking.openURL(
        `whatsapp://send?phone=${eventCheckout.value}&text=${message}`
      );
    }
    if (eventCheckout.type === "EMAIL") {
      Linking.openURL(`mailto:${eventCheckout.value}?subject=${message}`);
    }
    if (eventCheckout.type === "WEBSITE") {
      Linking.openURL(eventCheckout.value);
    }
    if (eventCheckout.type === "PHONE") {
      Linking.openURL(`tel:${eventCheckout.value}`);
    }
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isOpen}
      onRequestClose={onClose}
    >
      <View style={styles.centeredView}>
        <View style={styles.modalView}>
          <Text style={styles.title}>{t("event.participate_through")}</Text>
          <Gap y={globalStyles.gap.xs} />
          {checkouts.map((item, i) => (
            <View key={item.name + i}>
              <CheckoutItem
                type={item.type}
                title={item.name}
                onPress={() => handleOnPress(item)}
              />
              <Gap y={globalStyles.gap["2xs"]} />
            </View>
          ))}
          <Gap y={globalStyles.gap.xs} />
          <Button
            text={t("common.back")}
            onPress={onClose}
            type="secondary"
            style={{
              paddingVertical: 5,
            }}
          />
        </View>
      </View>
      <View style={styles.backdrop} />
    </Modal>
  );
};

const styles = StyleSheet.create({
  centeredView: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 22,
    elevation: 5,
  },
  modalView: {
    margin: 20,
    minWidth: "50%",
    backgroundColor: "white",
    borderRadius: globalStyles.rounded.sm,
    padding: globalStyles.gap.xs,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  backdrop: {
    zIndex: -1,
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(0,0,0,0.15)",
  },
  title: {
    color: globalStyles.rgba().dark.primary,
    fontSize: globalStyles.size["lg"],
    fontWeight: "bold",
  },
});

export default CheckoutModal;
