import { View, Text, TouchableOpacity } from "react-native";
import React from "react";
import globalStyles from "@/lib/globalStyles";

type Props = {
  description?: string;
  linkText: string;
  onPress?: () => void;
};

const InfoLink = ({ description, linkText, onPress }: Props) => {
  return (
    <View
      style={{
        flexDirection: "row",
        flexWrap: "wrap",
        justifyContent: "center",
      }}
    >
      {description && (
        <Text
          style={{
            textAlign: "center",
            color: globalStyles.colors.tertiary2,
          }}
        >
          {description}
        </Text>
      )}
      <TouchableOpacity onPress={onPress}>
        <Text
          style={{
            textAlign: "center",
            color: globalStyles.colors.primary1,
          }}
        >
          {" " + linkText}
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default InfoLink;
