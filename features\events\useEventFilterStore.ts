import { MyEventCategory } from "@/features/categories/model";
import { EventFilterFields } from "@/features/events/model";
import { create } from "zustand";

export type EventSortItem = {
  key: string;
  sortBy: EventFilterFields["sortBy"];
  sortOrder: EventFilterFields["sortOrder"];
};

export type FilterItem = {
  value: string | undefined;
  key:
    | "selectedCategory"
    | "selectedOrganizer"
    | "search"
    | "selectedStartDate"
    | "selectedLocation";
};

type EventFilterStoreFields = {
  search: string;
  sortBy?: EventSortItem["sortBy"];
  sortOrder?: EventFilterFields["sortOrder"];
  selectedCategory?: MyEventCategory;
  selectedOrganizer?: string;
  selectedLocation?: string;
  selectedStartDate?: Date;
  showFilters: boolean;
};

type Store = EventFilterStoreFields & {
  hasFilter: () => boolean;
  filtersList: () => FilterItem[];
  setSelectedOrganizer: (organizer: string) => void;
  setSelectedCategory: (category: MyEventCategory) => void;
  setSelectedLocation: (location: string) => void;
  setStartDate: (date?: Date) => void;
  setSearch: (search: string) => void;
  setSortBy: (sortBy?: EventFilterFields["sortBy"]) => void;
  setSortOrder: (sortOrder?: EventFilterFields["sortOrder"]) => void;
  setFilter: (
    filterKey: keyof EventFilterStoreFields,
    value:
      | EventFilterStoreFields[typeof filterKey]
      | (() => EventFilterStoreFields[typeof filterKey])
  ) => void;
  clearFilter: (type?: FilterItem["key"]) => void;
  setShowFilters: (show: boolean) => void;
};

const hasFilterImpl = (get: () => Store) =>
  [
    get().selectedCategory,
    get().selectedOrganizer,
    get().selectedStartDate,
    get().selectedLocation,
    get().search,
  ].some((v) => v);

const filterListImpl = (get: () => Store): FilterItem[] =>
  (
    [
      { value: get().selectedCategory?.name, key: "selectedCategory" },
      { value: get().selectedOrganizer, key: "selectedOrganizer" },
      {
        value: get().selectedStartDate?.toLocaleDateString(),
        key: "selectedStartDate",
      },
      { value: get().selectedLocation, key: "selectedLocation" },
      { value: get().search, key: "search" },
    ] satisfies FilterItem[]
  ).filter((v) => !!v.value);

const useEventFilterStore = create<Store>((set, get) => ({
  search: "",
  showFilters: true,
  setShowFilters: (show) => set({ showFilters: show }),
  hasFilter: () => hasFilterImpl(get),

  filtersList: () => filterListImpl(get),

  setSelectedOrganizer: (organizer?: string) =>
    set({ selectedOrganizer: organizer }),

  setStartDate: (date?: Date) => set({ selectedStartDate: date }),
  setSelectedLocation: (location?: string) =>
    set({ selectedLocation: location }),

  setSelectedCategory: (category?: MyEventCategory) =>
    set((prev) => ({
      selectedCategory: !category?._id ? undefined : category,
    })),

  setSearch: (search) => set({ search }),

  setSortBy: (sortBy?: EventFilterFields["sortBy"]) => set({ sortBy }),
  setSortOrder: (sortOrder?: EventFilterFields["sortOrder"]) =>
    set({ sortOrder }),

  clearFilter: (type) =>
    type
      ? set({ [type]: undefined })
      : set({
          sortBy: undefined,
          selectedCategory: undefined,
          selectedOrganizer: undefined,
          selectedStartDate: undefined,
          selectedLocation: undefined,
          search: "",
        }),

  setFilter: (filterKey, value) => set({ [filterKey]: value }),
}));

export default useEventFilterStore;

export const eventSortItems: EventSortItem[] = [
  {
    key: "highlight",
    sortBy: "highlightedUntil",
    sortOrder: "asc",
  },
  {
    key: "name",
    sortBy: "name",
    sortOrder: "asc",
  },
  {
    key: "relevance",
    sortBy: undefined,
    sortOrder: undefined,
  },
  {
    key: "higher_price",
    sortBy: "prices",
    sortOrder: "desc",
  },
  {
    key: "lower_price",
    sortBy: "prices",
    sortOrder: "asc",
  },
  {
    key: "farthest",
    sortBy: "startAt",
    sortOrder: "desc",
  },
  {
    key: "nearest",
    sortBy: "startAt",
    sortOrder: "asc",
  },
];
