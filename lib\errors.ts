import { Alert } from "react-native";
import { ZodError } from "zod";
import * as Sen<PERSON> from "@sentry/react-native";

export const handleErrors = ({
  error,
  message,
}: {
  error: any;
  message?: string;
}): string => {
  if (error instanceof ZodError || "issues" in error) {
    const errorMessages = error.errors?.map((err: any) => err.message) || [];
    return errorMessages.join("\n");
  }

  return message || "Ocorreu um erro, tente novamente.";
};

export const createHandleErrorDialog = ({
  title,
  btnText,
  message,
  error,
}: {
  error: Error;
  message?: string;
  title: string;
  btnText?: string;
}) => {
  !__DEV__ && Sentry.captureException(error);
  return Alert.alert(title, handleErrors({ error, message }), [
    {
      text: btnText || "OK",
    },
  ]);
};
