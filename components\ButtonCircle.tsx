import {
  StyleSheet,
  Pressable,
  PressableProps,
  StyleProp,
  ViewStyle,
  View,
} from "react-native";
import React from "react";
import globalStyles from "@/lib/globalStyles";

type Props = PressableProps & {
  style?: StyleProp<ViewStyle>;
  ref?: React.Ref<View>;
};

const ButtonCircle = ({ style, ...props }: Props) => {
  return (
    <Pressable
      {...props}
      style={[styles.button, style]}
      android_ripple={{
        color: globalStyles.colors.light.primary,
        radius: globalStyles.rounded.full,
        foreground: true,
      }}
    >
      {/* <Text>BackButton</Text> */}
    </Pressable>
  );
};

const styles = StyleSheet.create({
  button: {
    borderRadius: globalStyles.rounded.full,
    backgroundColor: globalStyles.colors.light.primary,
    width: 32,
    height: 32,
    justifyContent: "center",
    alignItems: "center",
    overflow: "hidden",
  },
});

export default ButtonCircle;
