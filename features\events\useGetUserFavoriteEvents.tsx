import useGetQuery from "@/hooks/useGetQuery";
import { API_URLS } from "@/config/api";
import { MyEventType } from "./model";

type Props = {
  userId: string;
};

const useGetUserFavoriteEvents = ({ userId }: Props) => {
  const { data, ...rest } = useGetQuery<MyEventType[]>({
    apiUrl: API_URLS.eventsFavorites,
    queryKey: `userFavoriteEvents`,
    initialData: [],
    query: { userId },
    enabled: false,
  });

  return {
    favoriteEvents: data,
    ...rest,
  };
};

export default useGetUserFavoriteEvents;
