import { API_URLS, fetchApi } from "@/config/api";
import { useEffect, useState } from "react";
import { mergePathnameQuery } from "@/config/query-utils";

type Props = {
  userId?: string;
  eventId?: string;
};

export const favoriteKey = ({ userId, eventId }: Props) => {
  return ["favoriteEvent", userId, eventId].filter(Boolean).join("_");
};

const useHasUserFavoritedEvent = (query: Props) => {
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState<boolean>(false);

  const handleFetch = async () => {
    if (!query.userId || !query.eventId || isLoading) {
      setIsLoading(false);
      return false;
    }

    setIsLoading(true);
    const res = await fetchApi<boolean>(
      mergePathnameQuery(API_URLS.eventsFavorites, query),
      {
        method: "GET",
        cache: "no-cache",
      }
    ).catch((err) => {
      setIsLoading(false);
    });

    setIsLoading(false);
    setData(res?.data || false);
    return res?.data ? res.data : false;
  };

  useEffect(() => {
    handleFetch();
  }, [query.userId, query.eventId]);

  return {
    setIsFavorite: setData,
    isFavorite: data,
    isLoading,
    refetch: handleFetch,
  };
};

export default useHasUserFavoritedEvent;
