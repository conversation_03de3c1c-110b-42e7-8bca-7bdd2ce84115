import { URL, URLSearchParams } from "react-native-url-polyfill";

const stringifyIfObject = (value: any) => {
  if (!["string", "number", "boolean"].includes(typeof value)) {
    return JSON.stringify(value);
  }

  return value;
};

const addQueryToUrl = (
  key: string,
  value?: string | number | boolean | object,
  first?: boolean
) => (value ? `${first ? "?" : "&"}${key}=${stringifyIfObject(value)}` : "");

/**
 * @deprecated use mergePathnameQuery instead
 */
export const buildUrl = (
  url: string,
  query?: Record<string, string | number | boolean | object>
) => {
  if (!query) return url;

  for (const key in query) {
    url.includes("?")
      ? (url = url.concat(addQueryToUrl(key, query[key])))
      : (url = url.concat(addQueryToUrl(key, query[key], true)));
  }

  return url;
};

export function mergePathnameQuery(
  pathname: string,
  query?: Record<
    string,
    string | number | boolean | object | undefined | Array<any>
  >
): string {
  if (!query) return pathname;
  const url = new URL(pathname, "http://localhost");

  const queryObj: Record<string, any> = {};

  url.searchParams.forEach((value, key) => {
    if (value === undefined) queryObj[key] = value;
  });

  for (const key in query) {
    if (query.hasOwnProperty(key)) {
      const value = query[key];
      if (value === undefined) continue;
      queryObj[key] = !["string", "number", "boolean"].includes(typeof value)
        ? JSON.stringify(value)
        : value;
    }
  }

  const searchParams = new URLSearchParams(queryObj);
  const search = searchParams.toString();
  return url.pathname + (search ? `?${search}` : "");
}
