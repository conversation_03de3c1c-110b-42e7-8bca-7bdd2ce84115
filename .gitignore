# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*

# typescript
*.tsbuildinfo

app-example

android/

client_secret_852691487581-uar65t6711vo8utcq3f8also8edfbih2.apps.googleusercontent.com.json

.env.local

firebase_private_key-zimbora-7546a-firebase-adminsdk-fbsvc-d1f38ad1ed.json

google-services.json
