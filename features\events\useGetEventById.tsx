import useGetQuery from "@/hooks/useGetQuery";
import { MyEventType } from "./model";
import { API_URLS } from "@/config/api";
type Props = {
  id: string;
  event?: MyEventType;
};
const useGetEventById = ({ id, event }: Props) => {
  const { data, ...rest } = useGetQuery<MyEventType | undefined>({
    apiUrl: API_URLS.eventById(id),
    initialData: event,
    queryKey: `event-${id}`,
    enabled: true,
  });
  return { event: data, ...rest };
};

export default useGetEventById;
