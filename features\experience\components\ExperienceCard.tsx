import React from "react";
import {
  ImageBackground,
  Text,
  TouchableOpacity,
  View,
  ViewStyle,
} from "react-native";
import { Feather } from "@expo/vector-icons";
import { useTranslation } from "react-i18next";
import { cn } from "@/lib/utils";
import { ExperienceType, EXPERIENCE_TYPES } from "@/features/experience/model";
import { ANGOLA_PROVINCES } from "@/lib/angola-provinces";
import { LinearGradient } from "expo-linear-gradient";

type ExperienceCardProps = {
  experience: ExperienceType;
  style?: ViewStyle;
  onPress?: () => void;
  onFavoritePress?: () => void;
  isFavorite?: boolean;
};

const ExperienceCard = ({
  experience,
  style,
  onPress,
  onFavoritePress,
  isFavorite = false,
}: ExperienceCardProps) => {
  const { t } = useTranslation();

  const media = experience.medias?.[0];
  const imageUrl = media?.urlSmall || media?.url;

  const experienceType = EXPERIENCE_TYPES[experience.type];

  const location = experience.locations?.[0];
  const additionalLocationsCount = (experience.locations?.length || 0) - 1;
  const locationText = location
    ? `${
        additionalLocationsCount > 0 ? `(+${additionalLocationsCount}) ` : ""
      }${location.name}, ${
        ANGOLA_PROVINCES[location.province] || location.province
      }`
    : "";

  return (
    <TouchableOpacity
      onPress={onPress}
      style={style}
      className="rounded-3xl overflow-hidden"
      activeOpacity={0.8}
    >
      <ImageBackground
        source={
          imageUrl
            ? { uri: imageUrl }
            : require("@/assets/images/experience-demo.jpg")
        }
        className="aspect-[16/9] justify-between p-5"
        resizeMode="cover"
      >
        <LinearGradient
          colors={["rgba(0,0,0,0.9)", "transparent"]}
          start={{ y: 1, x: 0 }}
          end={{ y: 0, x: 0 }}
          className="absolute inset-0"
        />

        <View className="flex-row justify-between items-start z-10">
          <View className="flex-row items-center gap-2 px-4 py-1 rounded-full bg-white/20 backdrop-blur-md">
            {experienceType.Icon && (
              <experienceType.Icon className="size-3 text-white" />
            )}
            <Text className="text-white text-sm">
              {EXPERIENCE_TYPES[experience.type]?.label(t) || experience.type}
            </Text>
          </View>

          <TouchableOpacity
            onPress={onFavoritePress}
            className="p-1.5 rounded-full bg-white/20 backdrop-blur-md"
            activeOpacity={0.7}
          >
            <Feather
              name="heart"
              className={cn(
                "text-[14px]",
                isFavorite ? "text-primary-1" : "text-white"
              )}
            />
          </TouchableOpacity>
        </View>

        <View className="z-10 gap-1">
          <Text className="text-white text-3xl font-semibold" numberOfLines={2}>
            {experience.name}
          </Text>

          <Text className="text-white/90 text-base" numberOfLines={2}>
            {experience.description}
          </Text>

          {locationText && (
            <View className="flex-row items-center  gap-1">
              <Feather name="map-pin" size={14} className="text-white/80" />
              <Text className="text-white/80 text-base" numberOfLines={1}>
                {locationText}
              </Text>
            </View>
          )}
        </View>
      </ImageBackground>
    </TouchableOpacity>
  );
};

export default ExperienceCard;
