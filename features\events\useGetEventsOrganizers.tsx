import useGetQuery from "@/hooks/useGetQuery";
import { API_URLS } from "@/config/api";
export type MyOrganizer = {
  name: string;
};
const useGetEventsOrganizers = () => {
  const { data, ...rest } = useGetQuery<MyOrganizer[]>({
    apiUrl: API_URLS.eventsOrganizers,
    queryKey: "eventsOrganizers",
    initialData: [],
  });
  return {
    organizers: data,
    ...rest,
  };
};

export default useGetEventsOrganizers;
