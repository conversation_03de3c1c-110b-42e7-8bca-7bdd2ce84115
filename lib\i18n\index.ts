import i18n from "i18next";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Platform } from "react-native";
import RNRestart from "react-native-restart";

import en from "./translations/en.json";
import pt from "./translations/pt.json";
import { getLocales } from "expo-localization";

export const resources = {
  en: { translation: en },
  pt: { translation: pt },
};

export type SupportedLanguages = keyof typeof resources;

export const LANGUAGE_KEY = "user-language";

export const changeAppLanguage = async (lng: SupportedLanguages) => {
  await i18n.changeLanguage(lng);
  await AsyncStorage.setItem(LANGUAGE_KEY, lng);

  if (Platform.OS === "android") {
    RNRestart.Restart();
  }
};

export const getCurrentLanguage = async () => {
  const storedLanguage = await AsyncStorage.getItem(LANGUAGE_KEY);
  if (storedLanguage) {
    return storedLanguage;
  }
  const deviceLanguage = getLocales()?.[0]?.languageCode || "pt";
  return resources[deviceLanguage as SupportedLanguages]
    ? deviceLanguage
    : "pt";
};
