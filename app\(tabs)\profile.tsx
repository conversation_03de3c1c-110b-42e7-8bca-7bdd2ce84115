import Button from "@/components/Button";
import Gap from "@/components/Gap";
import InputField from "@/components/InputField";
import Layout from "@/components/Layout";
import { RenderIf } from "@/components/RenderIf";
import AuthHeader from "@/features/auth/components/AuthHeader";
import UserFavoriteEvents from "@/features/events/UserFavoriteEvents";
import ProfileStatsAction from "@/features/profile/components/ProfileStatsAction";
import ProfileUserInfo from "@/features/profile/components/ProfileUserInfo";
import useAppAuth from "@/features/auth/hooks/useAppAuth";
import { createLogoutAlert } from "@/features/auth/logoutAlert";
import globalStyles from "@/lib/globalStyles";
import { router } from "expo-router";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  Modal,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    width: "100%",
    height: "100%",
    justifyContent: "center",
    alignItems: "center",
  },
  modalBackdrop: {
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    position: "absolute",
    inset: 0,
  },
  modalScroll: {
    flexDirection: "column",
    borderRadius: 16,
    backgroundColor: "white",
    position: "relative",
    maxWidth: "70%",
    maxHeight: "60%",
  },
  modalContent: {
    flex: 1,
    width: "100%",
    minHeight: "100%",
    alignContent: "center",
    justifyContent: "center",
    padding: globalStyles.size["3xl"],
    position: "relative",
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: globalStyles.colors.dark.secondary,
  },
  modalDescription: {
    fontSize: 14,
    color: globalStyles.colors.dark.secondary,
    marginTop: 8,
  },
  warningText: {
    fontSize: 14,
    color: globalStyles.rgba().light.secondary,
    marginTop: 8,
  },
  input: {
    width: "100%",
  },
});

export default function ProfileScreen() {
  const { user, isLoadingUser, signOut, deleteUser } = useAppAuth();
  const [showModal, setShowModal] = useState(false);
  const [deleteInput, setDeleteInput] = useState("");
  const { t } = useTranslation();

  const handleLogoutAlert = () => createLogoutAlert(t, signOut);

  const handleDeleteUser = async () => {
    setShowModal(false);
    setDeleteInput("");
    await deleteUser();
  };

  return (
    <>
      <Layout
        title={user ? { text: t("auth.your_profile") } : undefined}
        isLoading={isLoadingUser}
        noScroll
      >
        <View
          style={{
            paddingTop: globalStyles.size.pageTop,
            flexDirection: "column",
          }}
        >
          <RenderIf isTrue={!user}>
            <View style={{ flexDirection: "column", gap: globalStyles.gap.xs }}>
              <AuthHeader description={t("auth.auth_header_description")} />
              <Button
                text={t("auth.sign_in")}
                onPress={() => router.push("/sign-in")}
              />
              <Button
                text={t("auth.sign_up")}
                type="secondary"
                onPress={() => router.push("/sign-up")}
              />
            </View>
          </RenderIf>

          {user && (
            <>
              {user && <ProfileUserInfo user={user} />}
              <Gap y={globalStyles.gap.md} />
              <ProfileStatsAction
                onLogout={handleLogoutAlert}
                user={user}
                onDelete={() => setShowModal(true)}
              />
              <Gap y={globalStyles.gap.md} />
              <UserFavoriteEvents userId={user.id} />
            </>
          )}
        </View>
      </Layout>

      <Modal
        animationType="slide"
        transparent={true}
        visible={showModal}
        onRequestClose={() => setShowModal(false)}
      >
        <View style={styles.modalContainer}>
          <TouchableOpacity
            style={styles.modalBackdrop}
            onPress={() => setShowModal(false)}
          />
          <ScrollView style={styles.modalScroll}>
            <View style={styles.modalContent}>
              <Text style={styles.modalTitle}>
                {t("auth.want_delete_account")}
              </Text>
              <Text style={styles.warningText}>
                {t("auth.want_to_delete_account_description")}
              </Text>
              <Text style={styles.modalDescription}>
                {t("auth.want_delete_account_confirm")}
              </Text>
              <Text
                style={{
                  color: globalStyles.rgba().secondary2,
                }}
              >
                {user?.email}
              </Text>
              <Gap y={globalStyles.gap.xs} />
              <InputField
                autoCapitalize="none"
                autoComplete="off"
                placeholder={t("auth.type_your_email")}
                style={styles.input}
                onChangeText={(text) => setDeleteInput(text)}
                containerStyle={{ flexDirection: "row" }}
              />
              <Gap y={globalStyles.gap.sm} />
              <Button
                size="md"
                text={t("common.yes")}
                style={{ backgroundColor: globalStyles.rgba().secondary2 }}
                onPress={handleDeleteUser}
                isLoading={isLoadingUser}
                disabled={
                  deleteInput.toLowerCase() !==
                    user?.email.toLowerCase().trim() || isLoadingUser
                }
              />
            </View>
          </ScrollView>
        </View>
      </Modal>
    </>
  );
}
