import globalStyles from "@/lib/globalStyles";
import React from "react";
import { Image, Text, View } from "react-native";

type Props = {
  description: string;
};

const AuthHeader = ({ description }: Props) => {
  return (
    <View
      style={{
        flexDirection: "column",
        alignItems: "center",
        gap: globalStyles.gap.sm,
      }}
    >
      <Image
        source={require("@/assets/icons/logo.png")}
        style={{
          width: "60%",
          height: undefined,
          aspectRatio: 5 / 3,
          resizeMode: "contain",
        }}
      />
      <Text
        style={{
          textAlign: "center",
          fontSize: globalStyles.size.xl,
          color: globalStyles.colors.tertiary2,
        }}
      >
        {description}
      </Text>
    </View>
  );
};

export default AuthHeader;
