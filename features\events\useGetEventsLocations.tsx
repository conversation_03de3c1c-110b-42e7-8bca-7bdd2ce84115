import useGetQuery from "@/hooks/useGetQuery";
import { API_URLS } from "@/config/api";

export type MyLocation = {
  name: string;
};
const useGetEventsLocations = () => {
  const { data, ...rest } = useGetQuery<MyLocation[]>({
    apiUrl: API_URLS.eventsLocations,
    queryKey: "eventsLocations",
    initialData: [],
  });
  return {
    locations: data,
    ...rest,
  };
};

export default useGetEventsLocations;
